import React, {useEffect, useState} from 'react';
import {View, StyleSheet, Pressable, ScrollView, Modal, ActivityIndicator} from 'react-native';
import {useSelector} from 'react-redux';
import {MaterialCommunityIcons} from 'apptile-core';
import TextElement from '../../components-v2/base/TextElement';
import Button from '../../components-v2/base/Button';
import {PaddlePlan, getPaddleService} from '../../services/PaddleService';
import {EditorRootState} from '../../store/EditorRootState';
import theme from '../../styles-v2/theme';

interface PricingModalProps {
  visible: boolean;
  onClose: () => void;
}

const PricingModal: React.FC<PricingModalProps> = ({visible, onClose}) => {
  const [allPlans, setAllPlans] = useState<PaddlePlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<PaddlePlan | null>(null);
  const [billingInterval, setBillingInterval] = useState<'month' | 'year'>('month');

  const {user} = useSelector((state: EditorRootState) => state.user);
  const {orgId} = useSelector((state: EditorRootState) => state.apptile);

  useEffect(() => {
    if (visible) {
      loadPlans();
    }
  }, [visible]);

  const loadPlans = async () => {
    try {
      setLoading(true);
      setError(null);
      const paddleService = getPaddleService('test_f0ff365cf3b22228fc6dde90827');
      await paddleService.initialize();
      const fetchedPlans = await paddleService.fetchPlans(); // Use fetchPlans for real API data
      setAllPlans(fetchedPlans);
    } catch (err) {
      console.error('Failed to load plans:', err);
      setError('Failed to load pricing plans. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Filter plans based on billing interval
  const filteredPlans = allPlans.filter(plan =>
    plan.billing_cycle.interval === billingInterval
  );

  // Group plans by product for better organization
  const groupedPlans = filteredPlans.reduce((acc, plan) => {
    const productId = plan.product_id || 'default';
    if (!acc[productId]) {
      acc[productId] = [];
    }
    acc[productId].push(plan);
    return acc;
  }, {} as Record<string, PaddlePlan[]>);

  // Get display plans (one per product, preferring the current billing interval)
  const displayPlans = Object.values(groupedPlans).map(productPlans => {
    // Sort by billing interval preference and price
    return productPlans.sort((a, b) => {
      if (a.billing_cycle.interval === billingInterval && b.billing_cycle.interval !== billingInterval) return -1;
      if (b.billing_cycle.interval === billingInterval && a.billing_cycle.interval !== billingInterval) return 1;
      return parseFloat(a.unit_price.amount) - parseFloat(b.unit_price.amount);
    })[0];
  }).sort((a, b) => parseFloat(a.unit_price.amount) - parseFloat(b.unit_price.amount));

  const handlePlanSelect = (plan: PaddlePlan) => {
    setSelectedPlan(plan);
  };

  const handleUpgrade = async () => {
    if (!selectedPlan) return;

    try {
      const paddleService = getPaddleService();
      await paddleService.openCheckout({
        items: [
          {
            priceId: selectedPlan.id,
            quantity: 1,
          },
        ],
        customer: {
          email: user?.email,
          // Don't provide ID, just email for new customers
        },
        customData: {
          organizationId: orgId?.toString(),
          userId: user?.id?.toString(),
          credits: selectedPlan.custom_data?.credits,
        },
      });
    } catch (err) {
      console.error('Failed to open checkout:', err);
    }
  };

  const renderPlanCard = (plan: PaddlePlan) => {
    const isSelected = selectedPlan?.id === plan.id;
    const isPopular = plan.custom_data?.popular;
    const features = plan.custom_data?.features || [];
    const credits = plan.custom_data?.credits || plan.custom_data?.tokens || '0';

    // Calculate yearly savings if applicable
    const getYearlySavings = () => {
      if (billingInterval === 'year') {
        const monthlyEquivalent = allPlans.find(p =>
          p.product_id === plan.product_id && p.billing_cycle.interval === 'month'
        );
        if (monthlyEquivalent) {
          const yearlyPrice = parseFloat(plan.unit_price.amount);
          const monthlyPrice = parseFloat(monthlyEquivalent.unit_price.amount) * 12;
          const savings = monthlyPrice - yearlyPrice;
          const savingsPercent = Math.round((savings / monthlyPrice) * 100);
          return { savings, savingsPercent };
        }
      }
      return null;
    };

    const yearlySavings = getYearlySavings();

    return (
      <Pressable
        key={plan.id}
        style={[
          styles.planCard,
          isSelected && styles.planCardSelected,
          isPopular && styles.planCardPopular
        ]}
        onPress={() => handlePlanSelect(plan)}>

        {isPopular && (
          <View style={styles.popularBadge}>
            <TextElement fontSize="xs" color="SECONDARY" fontWeight="600" style={{color: theme.DEFAULT_COLOR}}>
              MOST POPULAR
            </TextElement>
          </View>
        )}

        {yearlySavings && (
          <View style={styles.savingsBadge}>
            <TextElement fontSize="xs" color="SUCCESS" fontWeight="600">
              Save {yearlySavings.savingsPercent}%
            </TextElement>
          </View>
        )}

        <View style={[styles.planHeader, {marginTop: isPopular ? 20 : 0}]}>
          <TextElement fontSize="xl" fontWeight="600" color="SECONDARY">
            {plan.name}
          </TextElement>
          <TextElement fontSize="sm" color="EDITOR_LIGHT_BLACK" style={styles.planDescription}>
            {plan.description}
          </TextElement>
        </View>

        <View style={styles.priceSection}>
          <View style={styles.priceContainer}>
            <TextElement fontSize="4xl" fontWeight="700" color="SECONDARY">
              ${plan.unit_price.amount}
            </TextElement>
            <TextElement fontSize="lg" color="EDITOR_LIGHT_BLACK">
              /{billingInterval}
            </TextElement>
          </View>

          {yearlySavings && (
            <TextElement fontSize="sm" color="SUCCESS" style={styles.savingsText}>
              Save ${yearlySavings.savings.toFixed(2)} per year
            </TextElement>
          )}

          {credits !== '0' && (
            <TextElement fontSize="sm" color="EDITOR_LIGHT_BLACK" style={styles.creditsText}>
              {credits} {plan.custom_data?.credits ? 'credits' : 'tokens'} / month
            </TextElement>
          )}
        </View>

        {features.length > 0 && (
          <View style={styles.featuresSection}>
            <TextElement fontSize="sm" color="SECONDARY" fontWeight="600" style={styles.featuresTitle}>
              What's included:
            </TextElement>
            <View style={styles.featuresList}>
              {features.slice(0, 5).map((feature, index) => (
                <View key={index} style={styles.featureItem}>
                  <MaterialCommunityIcons
                    name="check"
                    size={16}
                    color={theme.SUCCESS}
                    style={styles.checkIcon}
                  />
                  <TextElement fontSize="sm" color="SECONDARY" style={styles.featureText}>
                    {feature}
                  </TextElement>
                </View>
              ))}
            </View>
          </View>
        )}
      </Pressable>
    );
  };

  const renderContent = () => (
    <View style={styles.container}>
      <View style={styles.header}>
        <Pressable onPress={onClose} style={styles.closeButton}>
          <MaterialCommunityIcons name="close" size={24} color={theme.SECONDARY_COLOR} />
        </Pressable>
        <TextElement fontSize="3xl" fontWeight="600" color="SECONDARY" style={styles.title}>
          Pricing
        </TextElement>
        <TextElement fontSize="lg" color="EDITOR_LIGHT_BLACK" style={styles.subtitle}>
          Start for free. Upgrade to get the capacity that exactly matches your team's needs.
        </TextElement>
      </View>

      <View style={styles.billingToggle}>
        <Pressable
          style={[styles.toggleButton, billingInterval === 'month' && styles.toggleButtonActive]}
          onPress={() => setBillingInterval('month')}>
          <TextElement
            fontSize="sm"
            color={billingInterval === 'month' ? 'PRIMARY' : 'EDITOR_LIGHT_BLACK'}
            fontWeight={billingInterval === 'month' ? '600' : '400'}>
            Monthly
          </TextElement>
        </Pressable>
        <Pressable
          style={[styles.toggleButton, billingInterval === 'year' && styles.toggleButtonActive]}
          onPress={() => setBillingInterval('year')}>
          <TextElement
            fontSize="sm"
            color={billingInterval === 'year' ? 'PRIMARY' : 'EDITOR_LIGHT_BLACK'}
            fontWeight={billingInterval === 'year' ? '600' : '400'}>
            Yearly
          </TextElement>
        </Pressable>
      </View>

      <ScrollView style={styles.plansContainer} showsVerticalScrollIndicator={false}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.CTA} />
            <TextElement fontSize="lg" color="EDITOR_LIGHT_BLACK" style={{marginTop: 16}}>
              Loading plans...
            </TextElement>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <MaterialCommunityIcons name="alert-circle" size={48} color={theme.ERROR} />
            <TextElement fontSize="lg" color="ERROR" style={{marginTop: 16, textAlign: 'center'}}>
              {error}
            </TextElement>
            <Button
              color="CTA"
              size="MEDIUM"
              onPress={loadPlans}
              containerStyles={{marginTop: 16}}>
              Try Again
            </Button>
          </View>
        ) : displayPlans.length === 0 ? (
          <View style={styles.emptyContainer}>
            <MaterialCommunityIcons name="package-variant" size={48} color={theme.DISABLED} />
            <TextElement fontSize="lg" color="DISABLED" style={{marginTop: 16, textAlign: 'center'}}>
              No plans available for {billingInterval}ly billing
            </TextElement>
          </View>
        ) : (
          <View style={styles.plansGrid}>
            {displayPlans.map(plan => renderPlanCard(plan))}
          </View>
        )}
      </ScrollView>

      {selectedPlan && selectedPlan.id !== 'free-plan' && (
        <View style={styles.footer}>
          <Button color="CTA" size="LARGE" onPress={handleUpgrade} containerStyles={styles.upgradeButton}>
            Upgrade to {selectedPlan.name}
          </Button>
        </View>
      )}
    </View>
  );

  return (
    <Modal visible={visible} transparent={true} animationType="fade" onRequestClose={onClose}>
      <Pressable style={styles.modalBackground} onPress={onClose}>
        <Pressable style={styles.modalContainer} onPress={e => e.stopPropagation()}>
          {renderContent()}
        </Pressable>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: 1200,
    maxWidth: '95vw',
    maxHeight: '95vh',
    backgroundColor: theme.DEFAULT_COLOR,
    borderRadius: 20,
    padding: 0,
    overflow: 'hidden',
    shadowColor: theme.SECONDARY_COLOR,
    shadowOffset: {width: 0, height: 20},
    shadowOpacity: 0.15,
    shadowRadius: 40,
    elevation: 20,
  },
  header: {
    padding: 40,
    paddingBottom: 32,
    borderBottomWidth: 1,
    borderBottomColor: theme.QUATERNARY_BACKGROUND,
    alignItems: 'center',
    background: `linear-gradient(135deg, ${theme.DEFAULT_COLOR} 0%, ${theme.SECONDARY_BACKGROUND} 100%)`,
  },
  closeButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    padding: 12,
    zIndex: 1,
    borderRadius: 8,
    backgroundColor: theme.QUATERNARY_BACKGROUND,
    ...theme.hoverable,
  },
  title: {
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    textAlign: 'center',
    maxWidth: 600,
    lineHeight: 24,
  },
  billingToggle: {
    flexDirection: 'row',
    backgroundColor: theme.QUATERNARY_BACKGROUND,
    borderRadius: 12,
    padding: 6,
    margin: 32,
    marginBottom: 24,
    alignSelf: 'center',
    shadowColor: theme.SECONDARY_COLOR,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  toggleButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
    ...theme.hoverable,
  },
  toggleButtonActive: {
    backgroundColor: theme.DEFAULT_COLOR,
    shadowColor: theme.CTA,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  plansContainer: {
    flex: 1,
    paddingHorizontal: 32,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 300,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 300,
    paddingHorizontal: 40,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 300,
  },
  plansGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 24,
    justifyContent: 'center',
    paddingBottom: 32,
  },
  footer: {
    padding: 32,
    borderTopWidth: 1,
    borderTopColor: theme.QUATERNARY_BACKGROUND,
    backgroundColor: theme.SECONDARY_BACKGROUND,
  },
  upgradeButton: {
    width: '100%',
  },
  planCard: {
    width: 320,
    backgroundColor: theme.DEFAULT_COLOR,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: theme.QUATERNARY_BACKGROUND,
    padding: 28,
    position: 'relative',
    minHeight: 480,
    shadowColor: theme.SECONDARY_COLOR,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    ...theme.hoverable,
  },
  planCardSelected: {
    borderColor: theme.CTA,
    shadowColor: theme.CTA,
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
    transform: [{scale: 1.02}],
  },
  planCardPopular: {
    borderColor: theme.CTA,
    borderWidth: 3,
  },
  popularBadge: {
    position: 'absolute',
    top: -1,
    left: 0,
    right: 0,
    backgroundColor: theme.CTA,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    alignItems: 'center',
  },
  savingsBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: theme.SUCCESS_BACKGROUND,
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 8,
    alignItems: 'center',
  },
  planHeader: {
    marginBottom: 20,
  },
  planDescription: {
    marginTop: 8,
    lineHeight: 20,
  },
  priceSection: {
    marginBottom: 24,
    alignItems: 'center',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  savingsText: {
    marginBottom: 8,
    textAlign: 'center',
  },
  creditsText: {
    textAlign: 'center',
  },
  featuresSection: {
    flex: 1,
  },
  featuresTitle: {
    marginBottom: 12,
  },
  featuresList: {
    flex: 1,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 6,
  },
  checkIcon: {
    marginRight: 12,
  },
  featureText: {
    flex: 1,
    lineHeight: 20,
  },
});

export default PricingModal;
