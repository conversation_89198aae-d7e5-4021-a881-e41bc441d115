import React, {useState} from 'react';
import {View, StyleSheet} from 'react-native';
import TextElement from '../../components-v2/base/TextElement';
import Button from '../../components-v2/base/Button';
import PricingModalImproved from './PricingModalImproved';
import theme from '../../styles-v2/theme';

const PricingDemo: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);

  const openModal = () => {
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <TextElement fontSize="4xl" fontWeight="700" color="SECONDARY" style={styles.title}>
          Improved Pricing Modal Demo
        </TextElement>
        
        <TextElement fontSize="lg" color="EDITOR_LIGHT_BLACK" style={styles.description}>
          This demo showcases the enhanced pricing modal with the following improvements:
        </TextElement>

        <View style={styles.featuresList}>
          <View style={styles.featureItem}>
            <View style={styles.bullet} />
            <TextElement fontSize="md" color="SECONDARY">
              <TextElement fontWeight="600">Better Visual Design:</TextElement> Modern cards with shadows, gradients, and improved spacing
            </TextElement>
          </View>
          
          <View style={styles.featureItem}>
            <View style={styles.bullet} />
            <TextElement fontSize="md" color="SECONDARY">
              <TextElement fontWeight="600">Proper Billing Filtering:</TextElement> Shows only plans for selected billing interval (monthly/yearly)
            </TextElement>
          </View>
          
          <View style={styles.featureItem}>
            <View style={styles.bullet} />
            <TextElement fontSize="md" color="SECONDARY">
              <TextElement fontWeight="600">Paddle Features:</TextElement> Popular badges, savings indicators, feature lists, and product images
            </TextElement>
          </View>
          
          <View style={styles.featureItem}>
            <View style={styles.bullet} />
            <TextElement fontSize="md" color="SECONDARY">
              <TextElement fontWeight="600">Yearly Savings:</TextElement> Automatically calculates and displays savings for yearly plans
            </TextElement>
          </View>
          
          <View style={styles.featureItem}>
            <View style={styles.bullet} />
            <TextElement fontSize="md" color="SECONDARY">
              <TextElement fontWeight="600">Better Layout:</TextElement> Responsive grid layout instead of horizontal scroll
            </TextElement>
          </View>
          
          <View style={styles.featureItem}>
            <View style={styles.bullet} />
            <TextElement fontSize="md" color="SECONDARY">
              <TextElement fontWeight="600">Enhanced UX:</TextElement> Loading states, error handling, and empty states
            </TextElement>
          </View>
          
          <View style={styles.featureItem}>
            <View style={styles.bullet} />
            <TextElement fontSize="md" color="SECONDARY">
              <TextElement fontWeight="600">Real API Integration:</TextElement> Uses production Paddle API instead of mock data
            </TextElement>
          </View>
        </View>

        <Button 
          color="CTA" 
          size="LARGE" 
          onPress={openModal}
          containerStyles={styles.demoButton}>
          Open Pricing Modal
        </Button>

        <View style={styles.note}>
          <TextElement fontSize="sm" color="EDITOR_LIGHT_BLACK" style={styles.noteText}>
            Note: The modal uses real Paddle API data. Switch between Monthly and Yearly to see different plans and pricing.
          </TextElement>
        </View>
      </View>

      <PricingModalImproved 
        visible={modalVisible} 
        onClose={closeModal} 
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.SECONDARY_BACKGROUND,
    padding: 40,
  },
  content: {
    maxWidth: 800,
    alignSelf: 'center',
  },
  title: {
    textAlign: 'center',
    marginBottom: 24,
  },
  description: {
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  featuresList: {
    marginBottom: 40,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  bullet: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: theme.CTA,
    marginTop: 6,
    marginRight: 16,
  },
  demoButton: {
    alignSelf: 'center',
    paddingHorizontal: 48,
    marginBottom: 24,
  },
  note: {
    backgroundColor: theme.DEFAULT_COLOR,
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: theme.CTA,
  },
  noteText: {
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default PricingDemo;
